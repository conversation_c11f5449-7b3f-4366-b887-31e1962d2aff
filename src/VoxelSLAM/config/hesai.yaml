General:
  lid_topic: "/vlp_merged/pandar"
  imu_topic: "/imu_raw"
  # The file path of the map to be saved
  save_path: "/home/<USER>/ws_voxel_slam2_vscode/src/VoxelSLAM/"
  # The offline map in the save_path to be loaded
  # The number after the map name is the threshold for loop
  # '#' means the map is not loaded
  previous_map: "# site1_handheld_5: 0.50, 
                 # site1_handheld_4: 0.45,
                 # site1_handheld_3: 0.30,
                 # site1_handheld_2: 0.50"
  # The bag name played in this run
  bagname: "site1_handheld_0"
  lidar_type: 3
  blind: 0.4
  blindup: 100
  point_filter_num: 3

  # extrinsic_tran: [0.00567, -0.74593, 0.01149]
  # extrinsic_rota: [1.0000, 0.0014, 0.0076,
  #                  0.0076, 0.0154, -0.9999,
  #                 -0.0015,  0.9999, 0.0154]

  extrinsic_tran: [0.0, 0.180741, -0.1430]
  extrinsic_rota: [-0.9925, 0.1225, -0.0028,
                   0.0430, 0.3262,  -0.9443,
                  -0.1147, -0.9373, -0.3290]
  # The session is saved in save_path + bagname
  is_save_map: 0
  is_save_pose: 0

  # **新增：体素合并可视化功能控制**
  # 0: 禁用，1: 启用体素合并可视化
  is_save_merge_visualization: 0
  # 可视化文件保存的子目录名
  merge_vis_subdir: "voxel_merge_visualization"

Odometry:
  Topstart: -1
  Topend: -1
  Rearstart: 32
  Rearend: 64
  cov_gyr: 0.01
  cov_acc: 1
  rdw_gyr: 0.0001
  rdw_acc: 0.0001
  # indoor:0.1, outdoor:0.25, high altitude: 0.5
  down_size: 0.1 # 0.2
  dept_err: 0.01
  beam_err: 0.01 # 0.01
  # indoor: 0.5-1, high speed: 2, high altitude: 4
  voxel_size: 1 # 2
  # The plane criterion
  min_eigen_value: 0.0025
  # The number of degenrate scans to make the system reset
  degrade_bound: 100
  # For the MULRAN, the point has no timestamp
  point_notime: 0

LocalBA:
  win_size: 10
  max_layer: 2
  cov_gyr: 0.01
  cov_acc: 1
  rdw_gyr: 0.0001
  rdw_acc: 0.0001
  min_ba_point: 1
  # The plane criterion
  # plane_eigen_value_thre: [16.0, 16.0, 16.0, 16.0]
  plane_eigen_value_thre: [1.0, 1.0, 1.0, 1.0]
  imu_coef: 0.000025 # 0.0001
  thread_num: 8

Loop:
  # The criterion for loop closure
  # It can be adjusted according to the environment
  jud_default: 0.5 # 0.22 0.34
  # The criterion for ICP to ensure the success of two scans matching
  icp_eigval: 10 # 8
  # The threshold for the ratio of drift and travelling distance
  ratio_drift: 0.01
  curr_halt: 10
  prev_halt: 10
  # In previous map, accumulate acsize scans to generate BTC descriptor
  acsize: 10
  mgsize: 5
  # The parameters of BTC in normal and high altitude environment are differernt
  isHighFly: 0

GBA:
  # GBA use the coarse-to-fine strategy
  # These are the coarse parameters
  voxel_size: 1
  min_eigen_value: 0.01
  eigen_value_array: [2.0, 2.0, 2.0, 2.0]
  total_max_iter: 3
